{"$schema": "https://developer.microsoft.com/en-us/json-schemas/teams/v1.16/MicrosoftTeams.schema.json", "version": "1.0.0", "manifestVersion": "1.16", "id": "${{VITE_TEAMS_APP_ID}}", "name": {"short": "${{VITE_APP_NAME}}", "full": "Use Relic Ai Assistants for Healthcare in Microsoft Teams"}, "description": {"short": "Ai Assistants for Healthcare meeting your team members where they operate.", "full": "This app provides access to Relic Ai Assistants for Healthcare, allowing users to interact with AI-driven healthcare solutions directly within Microsoft Teams."}, "developer": {"name": "Relic Care, Inc.", "websiteUrl": "https://reliccare.com", "privacyUrl": "${{VITE_PRIVACY_LINK}}", "termsOfUseUrl": "https://reliccare.com"}, "icons": {"outline": "outline.png", "color": "color.png"}, "accentColor": "#95A72C", "staticTabs": [{"entityId": "relicApp", "name": "${{VITE_APP_NAME}}", "contentUrl": "https://${{VITE_TEAMS_TAB_APP_URL}}", "scopes": ["personal"]}], "validDomains": ["${{VITE_APP_HOST}}", "www.reliccare.com"], "permissions": ["identity", "messageTeamMembers"], "webApplicationInfo": {"id": "${{VITE_TEAMS_MICROSOFT_LOGIN_CLIENT_ID}}", "resource": "api://${{VITE_TEAMS_TAB_APP_URL}}/${{VITE_TEAMS_MICROSOFT_LOGIN_CLIENT_ID}}"}}